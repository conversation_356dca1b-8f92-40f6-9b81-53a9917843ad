version: '3.8'

services:
  # API service
  api:
    image: langgenius/dify-api:1.4.2
    restart: always
    environment:
      # Use postgres as the database for Dify.
      DB_USERNAME: postgres
      DB_PASSWORD: difyai123456
      DB_HOST: db
      DB_PORT: 5432
      DB_DATABASE: dify
      # redis configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_USERNAME: ''
      REDIS_PASSWORD: difyai123456
      REDIS_DB: 0
      # The configurations of celery broker.
      CELERY_BROKER_URL: redis://:difyai123456@redis:6379/1
      # The type of storage to use for storing user files. Supported values are `local` and `s3` and `azure-blob` and `google-storage`, Default: `local`
      STORAGE_TYPE: local
      STORAGE_LOCAL_PATH: storage
      # The Vector database configurations, support: `weaviate`, `qdrant`, `milvus`, `relyt`.
      VECTOR_STORE: weaviate
      WEAVIATE_ENDPOINT: http://weaviate:8080
      WEAVIATE_API_KEY: WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih
      # The DSN of sentry for monitoring purpose, keep empty to disable sentry.
      SENTRY_DSN: ''
      # API Tool configuration
      SECRET_KEY: ************************************************
      # The configurations of session, only support redis storage.
      SESSION_REDIS_HOST: redis
      SESSION_REDIS_PORT: 6379
      SESSION_REDIS_PASSWORD: difyai123456
      SESSION_REDIS_DB: 2
      # The configurations of code execution sandbox.
      CODE_EXECUTION_ENDPOINT: http://sandbox:8194
      CODE_EXECUTION_API_KEY: dify-sandbox
      CODE_MAX_NUMBER: 9223372036854775807
      CODE_MIN_NUMBER: -9223372036854775808
      CODE_MAX_STRING_LENGTH: 80000
      TEMPLATE_TRANSFORM_MAX_LENGTH: 80000
      CODE_MAX_STRING_ARRAY_LENGTH: 30
      CODE_MAX_OBJECT_ARRAY_LENGTH: 30
      CODE_MAX_NUMBER_ARRAY_LENGTH: 1000
      # SSRF Proxy server configuration
      SSRF_PROXY_HTTP_URL: http://ssrf_proxy:3128
      SSRF_PROXY_HTTPS_URL: http://ssrf_proxy:3128
      # Mail configuration, support: resend, smtp
      MAIL_TYPE: ''
      MAIL_DEFAULT_SEND_FROM: ''
      RESEND_API_KEY: ''
      RESEND_API_URL: https://api.resend.com
      # The default role for new users
      INIT_PASSWORD: ''
      CONSOLE_WEB_URL: ''
      CONSOLE_API_URL: ''
      SERVICE_API_URL: ''
      APP_WEB_URL: ''
      FILES_URL: ''
      FILES_ACCESS_TIMEOUT: 300
      # File preview or download Url prefix.
      # used to display File preview or download Url to the front-end or as Multi-model inputs;
      # Url is signed and has expiration time.
      S3_ENDPOINT: ''
      S3_BUCKET_NAME: ''
      S3_ACCESS_KEY: ''
      S3_SECRET_KEY: ''
      S3_REGION: us-east-1
      # The DSN of postgres database.
      SQLALCHEMY_DATABASE_URI: ******************************************/dify
      SQLALCHEMY_ECHO: 'false'
      # The configurations of file upload.
      UPLOAD_FILE_SIZE_LIMIT: 15
      UPLOAD_FILE_BATCH_LIMIT: 5
      UPLOAD_IMAGE_FILE_SIZE_LIMIT: 10
      # When enabled, migrations will be executed prior to application startup and the application will wait for completion.
      MIGRATION_ENABLED: 'true'
      # Indexing configuration
      INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH: 1000
      # Log level
      LOG_LEVEL: INFO
      DEBUG: 'false'
      FLASK_DEBUG: 'false'
      # Output stream configuration
      HTTP_REQUEST_MAX_CONNECT_TIMEOUT: 10
      HTTP_REQUEST_MAX_READ_TIMEOUT: 60
      HTTP_REQUEST_MAX_WRITE_TIMEOUT: 10
      HTTP_REQUEST_NODE_MAX_BINARY_SIZE: 10485760
      HTTP_REQUEST_NODE_MAX_TEXT_SIZE: 1048576
      # multi-model configuration
      MULTIMODAL_SEND_IMAGE_FORMAT: base64
      # Dataset configuration
      CLEAN_DAY_SETTING: 30
      # Workflow configuration
      WORKFLOW_MAX_EXECUTION_STEPS: 500
      WORKFLOW_MAX_EXECUTION_TIME: 1200
      WORKFLOW_CALL_MAX_DEPTH: 5
      # OPS trace configuration
      OPS_TRACE_ENABLED: 'false'
      LANGFUSE_PUBLIC_KEY: ''
      LANGFUSE_SECRET_KEY: ''
      LANGFUSE_HOST: ''
      LANGSMITH_API_KEY: ''
      LANGSMITH_PROJECT: ''
      TRACEABLE_API_KEY: ''
      TRACEABLE_PROJECT: ''
    depends_on:
      - db
      - redis
    volumes:
      # Mount the storage directory to the container, for storing user files.
      - ./volumes/app/storage:/app/api/storage
    networks:
      - ssrf_proxy_network
      - default

  # worker service
  # The Celery worker for processing the queue.
  worker:
    image: langgenius/dify-api:1.4.2
    restart: always
    environment:
      # Use postgres as the database for Dify.
      DB_USERNAME: postgres
      DB_PASSWORD: difyai123456
      DB_HOST: db
      DB_PORT: 5432
      DB_DATABASE: dify
      # redis configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_USERNAME: ''
      REDIS_PASSWORD: difyai123456
      REDIS_DB: 0
      # The configurations of celery broker.
      CELERY_BROKER_URL: redis://:difyai123456@redis:6379/1
      # The type of storage to use for storing user files. Supported values are `local` and `s3` and `azure-blob` and `google-storage`, Default: `local`
      STORAGE_TYPE: local
      STORAGE_LOCAL_PATH: storage
      # The Vector database configurations, support: `weaviate`, `qdrant`, `milvus`, `relyt`.
      VECTOR_STORE: weaviate
      WEAVIATE_ENDPOINT: http://weaviate:8080
      WEAVIATE_API_KEY: WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih
      # The DSN of sentry for monitoring purpose, keep empty to disable sentry.
      SENTRY_DSN: ''
      # API Tool configuration
      SECRET_KEY: ************************************************
      # The configurations of session, only support redis storage.
      SESSION_REDIS_HOST: redis
      SESSION_REDIS_PORT: 6379
      SESSION_REDIS_PASSWORD: difyai123456
      SESSION_REDIS_DB: 2
      # The configurations of code execution sandbox.
      CODE_EXECUTION_ENDPOINT: http://sandbox:8194
      CODE_EXECUTION_API_KEY: dify-sandbox
      CODE_MAX_NUMBER: 9223372036854775807
      CODE_MIN_NUMBER: -9223372036854775808
      CODE_MAX_STRING_LENGTH: 80000
      TEMPLATE_TRANSFORM_MAX_LENGTH: 80000
      CODE_MAX_STRING_ARRAY_LENGTH: 30
      CODE_MAX_OBJECT_ARRAY_LENGTH: 30
      CODE_MAX_NUMBER_ARRAY_LENGTH: 1000
      # SSRF Proxy server configuration
      SSRF_PROXY_HTTP_URL: http://ssrf_proxy:3128
      SSRF_PROXY_HTTPS_URL: http://ssrf_proxy:3128
      # Mail configuration, support: resend, smtp
      MAIL_TYPE: ''
      MAIL_DEFAULT_SEND_FROM: ''
      RESEND_API_KEY: ''
      RESEND_API_URL: https://api.resend.com
      # The default role for new users
      INIT_PASSWORD: ''
      CONSOLE_WEB_URL: ''
      CONSOLE_API_URL: ''
      SERVICE_API_URL: ''
      APP_WEB_URL: ''
      FILES_URL: ''
      FILES_ACCESS_TIMEOUT: 300
      # File preview or download Url prefix.
      # used to display File preview or download Url to the front-end or as Multi-model inputs;
      # Url is signed and has expiration time.
      S3_ENDPOINT: ''
      S3_BUCKET_NAME: ''
      S3_ACCESS_KEY: ''
      S3_SECRET_KEY: ''
      S3_REGION: us-east-1
      # The DSN of postgres database.
      SQLALCHEMY_DATABASE_URI: ******************************************/dify
      SQLALCHEMY_ECHO: 'false'
      # The configurations of file upload.
      UPLOAD_FILE_SIZE_LIMIT: 15
      UPLOAD_FILE_BATCH_LIMIT: 5
      UPLOAD_IMAGE_FILE_SIZE_LIMIT: 10
      # When enabled, migrations will be executed prior to application startup and the application will wait for completion.
      MIGRATION_ENABLED: 'false'
      # Indexing configuration
      INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH: 1000
      # Log level
      LOG_LEVEL: INFO
      DEBUG: 'false'
      FLASK_DEBUG: 'false'
      # Output stream configuration
      HTTP_REQUEST_MAX_CONNECT_TIMEOUT: 10
      HTTP_REQUEST_MAX_READ_TIMEOUT: 60
      HTTP_REQUEST_MAX_WRITE_TIMEOUT: 10
      HTTP_REQUEST_NODE_MAX_BINARY_SIZE: 10485760
      HTTP_REQUEST_NODE_MAX_TEXT_SIZE: 1048576
      # multi-model configuration
      MULTIMODAL_SEND_IMAGE_FORMAT: base64
      # Dataset configuration
      CLEAN_DAY_SETTING: 30
      # Workflow configuration
      WORKFLOW_MAX_EXECUTION_STEPS: 500
      WORKFLOW_MAX_EXECUTION_TIME: 1200
      WORKFLOW_CALL_MAX_DEPTH: 5
      # OPS trace configuration
      OPS_TRACE_ENABLED: 'false'
      LANGFUSE_PUBLIC_KEY: ''
      LANGFUSE_SECRET_KEY: ''
      LANGFUSE_HOST: ''
      LANGSMITH_API_KEY: ''
      LANGSMITH_PROJECT: ''
      TRACEABLE_API_KEY: ''
      TRACEABLE_PROJECT: ''
    depends_on:
      - db
      - redis
    volumes:
      # Mount the storage directory to the container, for storing user files.
      - ./volumes/app/storage:/app/api/storage
    command: celery -A app.celery worker -P gevent -c 1 --loglevel INFO -Q dataset,generation,mail,ops_trace,app_deletion
    networks:
      - ssrf_proxy_network
      - default

  # Frontend web application.
  web:
    image: langgenius/dify-web:1.4.2
    restart: always
    environment:
      CONSOLE_API_URL: ''
      APP_API_URL: ''
      SENTRY_DSN: ''
    networks:
      - ssrf_proxy_network
      - default

  # The postgres database.
  db:
    image: postgres:15-alpine
    restart: always
    environment:
      PGUSER: postgres
      POSTGRES_PASSWORD: difyai123456
      POSTGRES_DB: dify
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - ./volumes/db/data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD', 'pg_isready']
      interval: 1s
      timeout: 3s
      retries: 30

  # The redis cache.
  redis:
    image: redis:6-alpine
    restart: always
    volumes:
      - ./volumes/redis/data:/data
    command: redis-server --requirepass difyai123456
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']

  # The DifySandbox
  sandbox:
    image: langgenius/dify-sandbox:0.2.1
    restart: always
    environment:
      API_KEY: dify-sandbox
      GIN_MODE: 'release'
      WORKER_TIMEOUT: 15
    volumes:
      - ./volumes/sandbox/dependencies:/dependencies
    networks:
      - ssrf_proxy_network
      - default

  # ssrf_proxy server
  # for protect SSRF attack
  ssrf_proxy:
    image: ubuntu/squid:latest
    restart: always
    volumes:
      - ./ssrf_proxy/squid.conf:/etc/squid/squid.conf
    networks:
      - ssrf_proxy_network
      - default

  # Weaviate vector database.
  weaviate:
    image: semitechnologies/weaviate:1.19.0
    restart: always
    volumes:
      - ./volumes/weaviate:/var/lib/weaviate
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'false'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      CLUSTER_HOSTNAME: 'node1'
      AUTHENTICATION_APIKEY_ENABLED: 'true'
      AUTHENTICATION_APIKEY_ALLOWED_KEYS: 'WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih'
      AUTHENTICATION_APIKEY_USERS: '<EMAIL>'
      AUTHORIZATION_ADMINLIST_ENABLED: 'true'
      AUTHORIZATION_ADMINLIST_USERS: '<EMAIL>'

  # The nginx reverse proxy.
  # used for reverse proxying the API service and Web service.
  nginx:
    image: nginx:latest
    restart: always
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/proxy.conf:/etc/nginx/proxy.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - api
      - web
    ports:
      - "80:80"
      - "443:443"

networks:
  # create a network between sandbox, api and ssrf_proxy, and can not access outside.
  ssrf_proxy_network:
    driver: bridge
    internal: true
