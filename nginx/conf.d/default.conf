server {
    listen 80;
    server_name _;

    location /console/api {
        include proxy.conf;
        proxy_pass http://api:5001;
    }

    location /api {
        include proxy.conf;
        proxy_pass http://api:5001;
    }

    location /v1 {
        include proxy.conf;
        proxy_pass http://api:5001;
    }

    location /files {
        include proxy.conf;
        proxy_pass http://api:5001;
    }

    location / {
        include proxy.conf;
        proxy_pass http://web:3000;
    }

    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}
