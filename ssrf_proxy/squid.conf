# Squid configuration for SSRF protection

# Access control lists
acl localnet src 10.0.0.0/8
acl localnet src **********/12
acl localnet src ***********/16
acl localnet src fc00::/7
acl localnet src fe80::/10

# Safe ports
acl SSL_ports port 443
acl Safe_ports port 80
acl Safe_ports port 443
acl Safe_ports port 1025-65535
acl CONNECT method CONNECT

# Deny requests to localhost and private networks
acl localhost src *********/8
acl private_networks src 10.0.0.0/8 **********/12 ***********/16

# Allow access from local networks
http_access allow localnet

# Deny access to localhost and private networks
http_access deny localhost
http_access deny private_networks

# Only allow cachemgr access from localhost
http_access allow localhost manager
http_access deny manager

# Deny requests to certain unsafe ports
http_access deny !Safe_ports

# Deny CONNECT to other than secure SSL ports
http_access deny CONNECT !SSL_ports

# Allow all other access
http_access allow all

# <PERSON>quid normally listens to port 3128
http_port 3128

# Leave coredumps in the first cache dir
coredump_dir /var/spool/squid

# Add any of your own refresh_pattern entries above these.
refresh_pattern ^ftp:		1440	20%	10080
refresh_pattern ^gopher:	1440	0%	1440
refresh_pattern -i (/cgi-bin/|\?) 0	0%	0
refresh_pattern .		0	20%	4320

# Disable cache
cache deny all
